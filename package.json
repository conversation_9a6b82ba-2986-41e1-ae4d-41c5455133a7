{"name": "ever-works", "version": "0.1.0", "description": "Ever Works - Open Directory Builder Platform", "homepage": "https://ever.works", "repository": {"type": "git", "url": "https://github.com/ever-co/ever-works.git"}, "bugs": {"url": "https://github.com/ever-co/ever-works/issues"}, "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "private": true, "scripts": {"dev": "turbo start:dev", "build": "turbo build", "prepare:husky": "npx husky install .husky", "lint": "turbo lint", "type-check": "turbo type-check", "clean": "turbo clean", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "prepare": "husky"}, "keywords": [], "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "husky": "^9.1.7", "prettier": "^3.6.2", "prettier-eslint-cli": "^8.0.1", "prettier-plugin-tailwindcss": "^0.5.14", "turbo": "^2.5.4"}, "engines": {"node": ">=20", "pnpm": ">=9.9.0"}, "packageManager": "pnpm@10.12.4", "prettier": {"printWidth": 120, "singleQuote": true, "semi": true, "useTabs": true, "tabWidth": 4, "arrowParens": "always", "trailingComma": "none", "quoteProps": "as-needed", "trimTrailingWhitespace": true, "overrides": [{"files": "*.scss", "options": {"useTabs": false, "tabWidth": 2}}, {"files": "*.yml", "options": {"useTabs": false, "tabWidth": 2}}]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "snyk": true}