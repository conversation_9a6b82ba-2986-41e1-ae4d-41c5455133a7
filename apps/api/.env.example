# GitHub
GITHUB_APIKEY=''
GITHUB_OWNER=''

# Git
GIT_NAME=''
GIT_EMAIL=''

# Vercel
VERCEL_TOKEN=

# AI Configuration
AI_DEFAULT_PROVIDER='openai'
AI_FALLBACK_PROVIDERS='google,anthropic'

# OpenAI
OPENAI_API_KEY=
OPENAI_MODEL='gpt-4.1'
OPENAI_TEMPERATURE=0.7
OPENAI_MAX_TOKENS=8192

# OpenRouter
OPENROUTER_API_KEY=
OPENROUTER_MODEL='openai/gpt-4.1'
OPENROUTER_TEMPERATURE=0.7
OPENROUTER_MAX_TOKENS=8192

# Google AI (Gemini)
GOOGLE_API_KEY=
GOOGLE_MODEL='gemini-2.5-flash'
GOOGLE_TEMPERATURE=0.7
GOOGLE_MAX_TOKENS=8192

# Anthropic (Claude)
ANTHROPIC_API_KEY=
ANTHROPIC_MODEL='claude-3-5-sonnet-20241022'
ANTHROPIC_TEMPERATURE=0.7
ANTHROPIC_MAX_TOKENS=8192

# Mistral AI
MISTRAL_API_KEY=
MISTRAL_MODEL='mistral-large-latest'
MISTRAL_TEMPERATURE=0.7
MISTRAL_MAX_TOKENS=8192

# Groq
GROQ_API_KEY=
GROQ_MODEL='llama-3.1-70b-versatile'
GROQ_TEMPERATURE=0.7
GROQ_MAX_TOKENS=8192

# DeepSeek
DEEPSEEK_API_KEY=
DEEPSEEK_MODEL='deepseek-chat'
DEEPSEEK_TEMPERATURE=0.7
DEEPSEEK_MAX_TOKENS=8192
DEEPSEEK_BASE_URL='https://api.deepseek.com'

# Ollama
OLLAMA_API_KEY=
OLLAMA_MODEL='llama2'
OLLAMA_TEMPERATURE=0.7
OLLAMA_MAX_TOKENS=8192
OLLAMA_BASE_URL='http://localhost:11434/v1'

# Tavily
TAVILY_API_KEY=

# SEARCH SERVICE CONFIGURATION
# Options: 'tavily', 'naive'
EXTRACT_CONTENT_SERVICE='tavily'

# WEB SEARCH SERVICE CONFIGURATION
# Options: 'tavily', 'google-sr'
WEB_SEARCH_SERVICE='tavily'
