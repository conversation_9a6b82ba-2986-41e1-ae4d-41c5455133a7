// @ts-nocheck
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { DatabaseModule, DatabaseConfigurations } from '@packages/agent';

/**
 * Example API App Module
 * 
 * This shows how to configure the database for an API application.
 * API apps can use either in-memory or persistent SQLite depending on the environment.
 */
@Module({
  imports: [
    // Global configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    
    // Database configuration for API
    // Development: uses in-memory SQLite by default
    // Production: uses persistent SQLite file
    DatabaseModule,
    
    // Alternative: Use the configuration factory for specific environments
    // Development with in-memory database and logging
    // ...DatabaseConfigurations.apiDevelopment(),
    
    // Production with persistent database
    // ...DatabaseConfigurations.apiProduction('/var/lib/ever-works/api.db'),
    
    // Your other modules here
  ],
})
export class ApiAppModule {}

/**
 * Environment Variables for API:
 * 
 * Development:
 * APP_TYPE=api
 * NODE_ENV=development
 * DATABASE_IN_MEMORY=true (default)
 * DATABASE_LOGGING=true
 * 
 * Production:
 * APP_TYPE=api
 * NODE_ENV=production
 * DATABASE_IN_MEMORY=false
 * DATABASE_PATH=/var/lib/ever-works/api.db
 * DATABASE_LOGGING=false
 * 
 * Test:
 * NODE_ENV=test
 * (automatically uses in-memory database)
 */
