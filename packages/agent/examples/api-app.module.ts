// @ts-nocheck
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DatabaseModule, DatabaseConfigurations, createTypeOrmModule, createDatabaseOptions } from '@packages/agent';

/**
 * Example API App Module
 *
 * This shows how to configure the database for an API application.
 * API apps can use either in-memory or persistent SQLite depending on the environment.
 */
@Module({
  imports: [
    // Global configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),

    // Option 1: Use the default DatabaseModule (uses environment variables)
    // DatabaseModule,

    // Option 2: Use predefined configurations (RECOMMENDED)
    // Development
    createTypeOrmModule(DatabaseConfigurations.apiDevelopment()),

    // Production
    // createTypeOrmModule(DatabaseConfigurations.apiProduction('/var/lib/ever-works/api.db')),

    // Option 3: Custom configuration
    // TypeOrmModule.forRoot(createDatabaseOptions('api', {
    //   inMemory: false,
    //   databasePath: '/custom/path/api.db',
    //   logging: process.env.NODE_ENV === 'development'
    // })),

    // Your other modules here
  ],
})
export class ApiAppModule {}

/**
 * Environment Variables for API:
 * 
 * Development:
 * APP_TYPE=api
 * NODE_ENV=development
 * DATABASE_IN_MEMORY=true (default)
 * DATABASE_LOGGING=true
 * 
 * Production:
 * APP_TYPE=api
 * NODE_ENV=production
 * DATABASE_IN_MEMORY=false
 * DATABASE_PATH=/var/lib/ever-works/api.db
 * DATABASE_LOGGING=false
 * 
 * Test:
 * NODE_ENV=test
 * (automatically uses in-memory database)
 */
