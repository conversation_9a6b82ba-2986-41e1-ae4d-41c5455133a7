import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { DatabaseModule, DatabaseConfigurations } from '@packages/agent';

/**
 * Example CLI App Module
 * 
 * This shows how to configure the database for a CLI application.
 * CLI apps typically use persistent SQLite files stored in the user's home directory.
 */
@Module({
  imports: [
    // Global configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    
    // Database configuration for CLI
    // This will create a persistent SQLite file at ~/.ever-works/ever-works.db
    DatabaseModule,
    
    // Alternative: Use the configuration factory for more control
    // ...DatabaseConfigurations.cli(),
    
    // Your other modules here
  ],
})
export class CliAppModule {}

/**
 * To use this configuration, set the following environment variables:
 * 
 * APP_TYPE=cli
 * 
 * Optional overrides:
 * DATABASE_PATH=/custom/path/to/database.db
 * DATABASE_LOGGING=true
 */
