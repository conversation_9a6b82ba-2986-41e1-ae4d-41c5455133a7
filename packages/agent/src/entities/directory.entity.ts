import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm';

const directories = new Map<string, Directory>();

@Entity()
export class Directory {
	@PrimaryGeneratedColumn()
	id: number;

	@Column()
	name: string;

	@Column()
	slug: string;

	@Column()
	website: string;

	@Column()
	owner: string;

	@Column()
	companyName: string;

	@Column()
	organization: boolean;

	@Column()
	description: string;

	@Column('simple-json')
	readmeConfig: MarkdownReadmeConfig;

	static createMock(directory: Directory) {
		if (!directory.owner) {
			throw new Error('Owner is required');
		}

		directories.set(directory.slug, directory);
	}

	static async findMock(slug: string) {
		return directories.get(slug);
	}

	getDataRepo() {
		return `${this.slug}-data`;
	}

	getWebsiteRepo() {
		return `${this.slug}-website`;
	}
}

export interface MarkdownReadmeConfig {
	header?: string;
	overwrite_default_header?: boolean;

	footer?: string;
	overwrite_default_footer?: boolean;
}
