import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { Directory } from '../entities/directory.entity';
import { User } from '../entities/user.entity';
import { DirectoryRepository } from './directory.repository';
import { databaseConfig } from './database.config';
import * as fs from 'fs';
import * as path from 'path';

@Module({
  imports: [
    ConfigModule.forFeature(databaseConfig),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        const dbConfig = configService.get('database');

        // Ensure directory exists for file-based SQLite databases
        if (dbConfig.database !== ':memory:' && !dbConfig.database.startsWith(':')) {
          const dbDir = path.dirname(dbConfig.database);
          if (!fs.existsSync(dbDir)) {
            fs.mkdirSync(dbDir, { recursive: true });
          }
        }

        return dbConfig;
      },
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([Directory, User]),
  ],
  providers: [DirectoryRepository],
  exports: [TypeOrmModule, DirectoryRepository],
})
export class DatabaseModule {}
