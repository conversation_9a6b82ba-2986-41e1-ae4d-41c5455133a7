import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { databaseConfig, DatabaseType } from './database.config';

/**
 * Factory function to create database configuration for different app types
 * @param appType - 'cli' | 'api' | 'test'
 * @param options - Additional configuration options
 */
export function createDatabaseConfig(
	appType: 'cli' | 'api' | 'test' = 'api',
	options: {
		// SQLite options
		databasePath?: string;
		inMemory?: boolean;
		// Database type
		databaseType?: DatabaseType;
		// Connection options for PostgreSQL/MySQL/MariaDB
		host?: string;
		port?: number;
		username?: string;
		password?: string;
		databaseName?: string;
		// Common options
		logging?: boolean;
	} = {}
) {
	// Set environment variables for the database configuration
	if (options.databaseType) {
		process.env.DATABASE_TYPE = options.databaseType;
	}

	if (options.databasePath) {
		process.env.DATABASE_PATH = options.databasePath;
	}

	if (options.inMemory !== undefined) {
		process.env.DATABASE_IN_MEMORY = options.inMemory.toString();
	}

	if (options.host) {
		process.env.DATABASE_HOST = options.host;
	}

	if (options.port) {
		process.env.DATABASE_PORT = options.port.toString();
	}

	if (options.username) {
		process.env.DATABASE_USERNAME = options.username;
	}

	if (options.password) {
		process.env.DATABASE_PASSWORD = options.password;
	}

	if (options.databaseName) {
		process.env.DATABASE_NAME = options.databaseName;
	}

	if (options.logging !== undefined) {
		process.env.DATABASE_LOGGING = options.logging.toString();
	}

	// Set the app type
	process.env.APP_TYPE = appType;

	return [
		ConfigModule.forFeature(databaseConfig),
		TypeOrmModule.forRootAsync({
			imports: [ConfigModule],
			useFactory: async (configService: any) => {
				const dbConfig = configService.get('database');

				// Ensure directory exists for file-based SQLite databases
				if (dbConfig.database !== ':memory:' && !dbConfig.database.startsWith(':')) {
					const fs = await import('fs');
					const path = await import('path');
					const dbDir = path.dirname(dbConfig.database);
					if (!fs.existsSync(dbDir)) {
						fs.mkdirSync(dbDir, { recursive: true });
					}
				}

				return dbConfig;
			},
			inject: ['ConfigService']
		})
	];
}

/**
 * Predefined configurations for common use cases
 */
export const DatabaseConfigurations = {
	/**
	 * CLI configuration - uses persistent SQLite file in user's home directory
	 */
	cli: () => createDatabaseConfig('cli'),

	/**
	 * API development configuration - uses in-memory SQLite by default
	 */
	apiDevelopment: () => createDatabaseConfig('api', { inMemory: true, logging: true }),

	/**
	 * API production configuration - uses persistent SQLite file
	 */
	apiProduction: (databasePath?: string) =>
		createDatabaseConfig('api', {
			inMemory: false,
			logging: false,
			databasePath
		}),

	/**
	 * Test configuration - always uses in-memory database
	 */
	test: () => createDatabaseConfig('test', { inMemory: true, logging: false }),

	/**
	 * PostgreSQL configuration for production
	 */
	postgres: (
		options: {
			host?: string;
			port?: number;
			username?: string;
			password?: string;
			databaseName?: string;
			logging?: boolean;
		} = {}
	) =>
		createDatabaseConfig('api', {
			databaseType: 'postgres',
			host: options.host || 'localhost',
			port: options.port || 5432,
			username: options.username || 'postgres',
			password: options.password || '',
			databaseName: options.databaseName || 'ever_works',
			logging: options.logging || false
		}),

	/**
	 * MySQL configuration for production
	 */
	mysql: (
		options: {
			host?: string;
			port?: number;
			username?: string;
			password?: string;
			databaseName?: string;
			logging?: boolean;
		} = {}
	) =>
		createDatabaseConfig('api', {
			databaseType: 'mysql',
			host: options.host || 'localhost',
			port: options.port || 3306,
			username: options.username || 'root',
			password: options.password || '',
			databaseName: options.databaseName || 'ever_works',
			logging: options.logging || false
		}),

	/**
	 * MariaDB configuration for production
	 */
	mariadb: (
		options: {
			host?: string;
			port?: number;
			username?: string;
			password?: string;
			databaseName?: string;
			logging?: boolean;
		} = {}
	) =>
		createDatabaseConfig('api', {
			databaseType: 'mariadb',
			host: options.host || 'localhost',
			port: options.port || 3306,
			username: options.username || 'root',
			password: options.password || '',
			databaseName: options.databaseName || 'ever_works',
			logging: options.logging || false
		})
};
