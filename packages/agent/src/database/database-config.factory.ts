import { TypeOrmModule, TypeOrmModuleOptions } from '@nestjs/typeorm';
import { DatabaseType, DatabaseConfig } from './database.config';
import { Directory } from '../entities/directory.entity';
import { User } from '../entities/user.entity';
import * as path from 'path';
import * as os from 'os';

/**
 * Direct configuration factory that returns TypeORM options
 * This avoids environment variable pollution and duplication
 */
export function createDatabaseOptions(
	appType: 'cli' | 'api' | 'test' = 'api',
	options: {
		// SQLite options
		databasePath?: string;
		inMemory?: boolean;
		// Database type
		databaseType?: DatabaseType;
		// Connection options for PostgreSQL/MySQL/MariaDB
		host?: string;
		port?: number;
		username?: string;
		password?: string;
		databaseName?: string;
		// Common options
		logging?: boolean;
		synchronize?: boolean;
	} = {}
): TypeOrmModuleOptions {
	const environment = process.env.NODE_ENV || 'development';
	const dbType = options.databaseType || 'sqlite';

	const baseConfig = {
		entities: [Directory, User],
		synchronize: options.synchronize ?? (environment !== 'production'),
		logging: options.logging ?? false,
	};

	// SQLite configuration
	if (dbType === 'sqlite') {
		let database: string;

		if (options.databasePath) {
			database = options.databasePath;
		} else if (options.inMemory) {
			database = ':memory:';
		} else if (appType === 'cli') {
			const dbDir = path.join(os.homedir(), '.ever-works');
			database = path.join(dbDir, 'ever-works.db');
		} else if (environment === 'test') {
			database = ':memory:';
		} else {
			database = ':memory:'; // Default to in-memory for API
		}

		// Ensure directory exists for file-based SQLite databases
		if (database !== ':memory:' && !database.startsWith(':')) {
			const fs = require('fs');
			const dbDir = path.dirname(database);
			if (!fs.existsSync(dbDir)) {
				fs.mkdirSync(dbDir, { recursive: true });
			}
		}

		return {
			...baseConfig,
			type: 'sqlite',
			database,
		} as TypeOrmModuleOptions;
	}

	// PostgreSQL configuration
	if (dbType === 'postgres') {
		return {
			...baseConfig,
			type: 'postgres',
			host: options.host || 'localhost',
			port: options.port || 5432,
			username: options.username || 'postgres',
			password: options.password || '',
			database: options.databaseName || 'ever_works',
		} as TypeOrmModuleOptions;
	}

	// MySQL configuration
	if (dbType === 'mysql') {
		return {
			...baseConfig,
			type: 'mysql',
			host: options.host || 'localhost',
			port: options.port || 3306,
			username: options.username || 'root',
			password: options.password || '',
			database: options.databaseName || 'ever_works',
		} as TypeOrmModuleOptions;
	}

	// MariaDB configuration
	if (dbType === 'mariadb') {
		return {
			...baseConfig,
			type: 'mariadb',
			host: options.host || 'localhost',
			port: options.port || 3306,
			username: options.username || 'root',
			password: options.password || '',
			database: options.databaseName || 'ever_works',
		} as TypeOrmModuleOptions;
	}

	// Default to SQLite in-memory
	return {
		...baseConfig,
		type: 'sqlite',
		database: ':memory:',
	} as TypeOrmModuleOptions;
}

/**
 * Helper function to create TypeORM module with custom options
 * Use this when you want to provide your own TypeORM configuration
 */
export function createTypeOrmModule(options: TypeOrmModuleOptions) {
	return TypeOrmModule.forRoot(options);
}

/**
 * Predefined configurations for common use cases
 * These return TypeORM options directly
 */
export const DatabaseConfigurations = {
	/**
	 * CLI configuration - uses persistent SQLite file in user's home directory
	 */
	cli: () => createDatabaseOptions('cli'),

	/**
	 * API development configuration - uses in-memory SQLite by default
	 */
	apiDevelopment: () => createDatabaseOptions('api', { inMemory: true, logging: true }),

	/**
	 * API production configuration - uses persistent SQLite file
	 */
	apiProduction: (databasePath?: string) =>
		createDatabaseOptions('api', {
			inMemory: false,
			logging: false,
			databasePath
		}),

	/**
	 * Test configuration - always uses in-memory database
	 */
	test: () => createDatabaseOptions('test', { inMemory: true, logging: false }),

	/**
	 * PostgreSQL configuration for production
	 */
	postgres: (
		options: {
			host?: string;
			port?: number;
			username?: string;
			password?: string;
			databaseName?: string;
			logging?: boolean;
		} = {}
	) =>
		createDatabaseOptions('api', {
			databaseType: 'postgres',
			host: options.host || 'localhost',
			port: options.port || 5432,
			username: options.username || 'postgres',
			password: options.password || '',
			databaseName: options.databaseName || 'ever_works',
			logging: options.logging || false
		}),

	/**
	 * MySQL configuration for production
	 */
	mysql: (
		options: {
			host?: string;
			port?: number;
			username?: string;
			password?: string;
			databaseName?: string;
			logging?: boolean;
		} = {}
	) =>
		createDatabaseOptions('api', {
			databaseType: 'mysql',
			host: options.host || 'localhost',
			port: options.port || 3306,
			username: options.username || 'root',
			password: options.password || '',
			databaseName: options.databaseName || 'ever_works',
			logging: options.logging || false
		}),

	/**
	 * MariaDB configuration for production
	 */
	mariadb: (
		options: {
			host?: string;
			port?: number;
			username?: string;
			password?: string;
			databaseName?: string;
			logging?: boolean;
		} = {}
	) =>
		createDatabaseOptions('api', {
			databaseType: 'mariadb',
			host: options.host || 'localhost',
			port: options.port || 3306,
			username: options.username || 'root',
			password: options.password || '',
			databaseName: options.databaseName || 'ever_works',
			logging: options.logging || false
		})
};
