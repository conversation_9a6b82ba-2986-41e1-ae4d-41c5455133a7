import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { databaseConfig } from './database.config';

/**
 * Factory function to create database configuration for different app types
 * @param appType - 'cli' | 'api' | 'test'
 * @param options - Additional configuration options
 */
export function createDatabaseConfig(
  appType: 'cli' | 'api' | 'test' = 'api',
  options: {
    databasePath?: string;
    inMemory?: boolean;
    logging?: boolean;
  } = {}
) {
  // Set environment variables for the database configuration
  if (options.databasePath) {
    process.env.DATABASE_PATH = options.databasePath;
  }
  
  if (options.inMemory !== undefined) {
    process.env.DATABASE_IN_MEMORY = options.inMemory.toString();
  }
  
  if (options.logging !== undefined) {
    process.env.DATABASE_LOGGING = options.logging.toString();
  }
  
  // Set the app type
  process.env.APP_TYPE = appType;

  return [
    ConfigModule.forFeature(databaseConfig),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: any) => {
        const dbConfig = configService.get('database');
        
        // Ensure directory exists for file-based SQLite databases
        if (dbConfig.database !== ':memory:' && !dbConfig.database.startsWith(':')) {
          const fs = await import('fs');
          const path = await import('path');
          const dbDir = path.dirname(dbConfig.database);
          if (!fs.existsSync(dbDir)) {
            fs.mkdirSync(dbDir, { recursive: true });
          }
        }
        
        return dbConfig;
      },
      inject: ['ConfigService'],
    }),
  ];
}

/**
 * Predefined configurations for common use cases
 */
export const DatabaseConfigurations = {
  /**
   * CLI configuration - uses persistent SQLite file in user's home directory
   */
  cli: () => createDatabaseConfig('cli'),
  
  /**
   * API development configuration - uses in-memory SQLite by default
   */
  apiDevelopment: () => createDatabaseConfig('api', { inMemory: true, logging: true }),
  
  /**
   * API production configuration - uses persistent SQLite file
   */
  apiProduction: (databasePath?: string) => createDatabaseConfig('api', { 
    inMemory: false, 
    logging: false,
    databasePath 
  }),
  
  /**
   * Test configuration - always uses in-memory database
   */
  test: () => createDatabaseConfig('test', { inMemory: true, logging: false }),
};
