# Database Configuration

This package provides TypeORM configuration for SQLite databases with support for different environments and app types.

## Usage

### Basic Setup

Import the `DatabaseModule` in your app module:

```typescript
import { Module } from '@nestjs/common';
import { DatabaseModule } from '@packages/agent';

@Module({
  imports: [DatabaseModule],
  // ... other configuration
})
export class AppModule {}
```

### Environment-Specific Configuration

The database configuration automatically adapts based on environment variables:

#### Environment Variables

- `APP_TYPE`: `'cli' | 'api'` - Determines the default database behavior
- `DATABASE_PATH`: Explicit path to SQLite database file
- `DATABASE_IN_MEMORY`: `'true' | 'false'` - Force in-memory or file-based database
- `DATABASE_LOGGING`: `'true' | 'false'` - Enable/disable SQL logging
- `NODE_ENV`: `'development' | 'production' | 'test'` - Environment mode

#### Default Behavior

| App Type | Environment | Default Database | Location |
|----------|-------------|------------------|----------|
| CLI | Any | File | `~/.ever-works/ever-works.db` |
| API | Development | In-memory | `:memory:` |
| API | Production | File | `/tmp/ever-works-api.db` |
| Any | Test | In-memory | `:memory:` |

### Using the Configuration Factory

For more control, use the configuration factory:

```typescript
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DatabaseConfigurations } from '@packages/agent';

@Module({
  imports: [
    // CLI app with persistent database
    ...DatabaseConfigurations.cli(),
    
    // API development with in-memory database
    ...DatabaseConfigurations.apiDevelopment(),
    
    // API production with custom database path
    ...DatabaseConfigurations.apiProduction('/path/to/database.db'),
    
    // Test environment
    ...DatabaseConfigurations.test(),
    
    TypeOrmModule.forFeature([Directory, User]),
  ],
})
export class AppModule {}
```

### Using the Repository

Inject the `DirectoryRepository` in your services:

```typescript
import { Injectable } from '@nestjs/common';
import { DirectoryRepository } from '@packages/agent';

@Injectable()
export class MyService {
  constructor(private readonly directoryRepository: DirectoryRepository) {}

  async createDirectory(data: Partial<Directory>) {
    return await this.directoryRepository.create(data);
  }

  async findDirectory(slug: string) {
    return await this.directoryRepository.findBySlug(slug);
  }
}
```

## Migration from Mock Methods

The old static mock methods have been replaced:

```typescript
// Old way (removed)
Directory.createMock(directory);
const directory = await Directory.findMock(slug);

// New way
const directory = await this.directoryRepository.create(directoryData);
const directory = await this.directoryRepository.findBySlug(slug);
```

## Database Schema

The SQLite database will be automatically created with the following tables:

- `directory` - Stores directory information
- `user` - Stores user information (if using User entity)

The schema is automatically synchronized in development mode (`synchronize: true`).
