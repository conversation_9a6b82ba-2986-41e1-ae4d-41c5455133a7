import { registerAs } from '@nestjs/config';
import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { Directory } from '../entities/directory.entity';
import { User } from '../entities/user.entity';
import * as path from 'path';
import * as os from 'os';

export interface DatabaseConfig {
  type: 'sqlite';
  database: string;
  entities: any[];
  synchronize: boolean;
  logging: boolean;
}

export const databaseConfig = registerAs('database', (): DatabaseConfig => {
  const environment = process.env.NODE_ENV || 'development';
  const appType = process.env.APP_TYPE || 'api'; // 'cli' or 'api'
  
  let database: string;
  
  if (process.env.DATABASE_PATH) {
    // Explicit database path provided
    database = process.env.DATABASE_PATH;
  } else if (appType === 'cli') {
    // CLI apps use persistent SQLite file
    const dbDir = path.join(os.homedir(), '.ever-works');
    database = path.join(dbDir, 'ever-works.db');
  } else if (environment === 'test') {
    // Test environment uses in-memory database
    database = ':memory:';
  } else {
    // API apps default to in-memory for development, can be overridden
    database = process.env.DATABASE_IN_MEMORY === 'false' 
      ? path.join(os.tmpdir(), 'ever-works-api.db')
      : ':memory:';
  }

  return {
    type: 'sqlite',
    database,
    entities: [Directory, User],
    synchronize: environment !== 'production',
    logging: process.env.DATABASE_LOGGING === 'true',
  };
});

export const getDatabaseConfig = (): TypeOrmModuleOptions => {
  const config = databaseConfig();
  return config as TypeOrmModuleOptions;
};
